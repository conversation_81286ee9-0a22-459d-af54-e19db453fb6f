import telebot
import time

# To run this bot, you need to install the pyTelegramBotAPI library:
# pip install pyTelegramBotAPI

BOT_TOKEN = "**********************************************"

bot = telebot.TeleBot(BOT_TOKEN)

from telebot import types

@bot.message_handler(commands=['start'])
def send_welcome(message):
    bot.send_message(message.chat.id, "Welcome!", reply_markup=types.ReplyKeyboardRemove())
    caption_text = "🎉 Welcome to Canva Pro Bot!\n\nYou can use this bot to get a FREE LIFETIME CANVA PRO subscription.💖"
    markup = types.InlineKeyboardMarkup(row_width=1)
    itembtn1 = types.InlineKeyboardButton('🎁 Get canva for FREE 🎁', callback_data='get_free')
    itembtn2 = types.InlineKeyboardButton('💎 Buy canva pro (subscription) 💎', callback_data='buy_pro')
    markup.add(itembtn1, itembtn2)
    bot.send_message(message.chat.id, caption_text, reply_markup=markup)

@bot.message_handler(func=lambda message: True)
def echo_all(message):
    bot.reply_to(message, message.text)

@bot.callback_query_handler(func=lambda call: call.data == 'get_free')
def callback_get_free(call):
    markup = types.InlineKeyboardMarkup(row_width=2)
    channel1_btn = types.InlineKeyboardButton('Channel 1', url='https://t.me/titanium_bots_channel')
    channel2_btn = types.InlineKeyboardButton('Channel 2', url='https://t.me/CANVA_PRO_FREE')
    check_joined_btn = types.InlineKeyboardButton('✅ Check Joined', callback_data='check_joined')
    markup.row(channel1_btn, channel2_btn)
    markup.add(check_joined_btn)
    bot.send_message(call.message.chat.id, "🚫 Channel Check!\n\nYou need to join our VIP channels to get your free Canva Pro. Please tap the buttons below to join and then hit 'Check Joined'.", reply_markup=markup)

@bot.callback_query_handler(func=lambda call: call.data == 'check_joined')
def callback_check_joined(call):
    # First display the confirmation message
    bot.send_message(call.message.chat.id, "🎉 Awesome! You've joined both channels.")

    # Start loading animation with initial 0%
    loading_message = bot.send_message(call.message.chat.id, "Loading... 0%")

    # Progress through 10%, 20%, 30%, 40%, 50%, 60%, 70%, 80%, 90%, 100%
    # (0% is already shown when the message is first sent)
    for percentage in range(10, 101, 10):
        time.sleep(0.5)  # Half-second delay for visible loading effect
        bot.edit_message_text(
            chat_id=call.message.chat.id,
            message_id=loading_message.message_id,
            text=f"Loading... {percentage}%"
        )

    # Replace the loading message with the claim message and buttons
    claim_markup = types.InlineKeyboardMarkup(row_width=2)
    claim_yes_btn = types.InlineKeyboardButton('✅ Yes, Claim it', callback_data='claim_yes')
    claim_no_btn = types.InlineKeyboardButton('❌ No, Cancel', callback_data='claim_no')
    claim_markup.row(claim_yes_btn, claim_no_btn)

    bot.edit_message_text(
        chat_id=call.message.chat.id,
        message_id=loading_message.message_id,
        text="🎊 Boom! Your free Canva Pro subscription is ready!\n\nWanna claim it?",
        reply_markup=claim_markup
    )

@bot.callback_query_handler(func=lambda call: call.data == 'claim_yes')
def callback_claim_yes(call):
    # Generate a unique referral link for this user
    user_id = call.from_user.id
    referral_link = f"https://t.me/canva_pro_robot?start={user_id}"

    # Create the referral message content
    referral_text = f"""👍 Referral Fiesta!

You're on board! Now, let's get those referrals rolling. Here's your referral info:

🔗 Your Magic Referral Link:

{referral_link}

Progress: ⚪⚪⚪⚪⚪ (0/5)

Share this link with your buddies to earn more referrals!

⚡ Complete your 5 referrals now & get a LIFETIME FREE Canva Pro subscription instantly!"""

    # Create the share text for direct sharing
    share_text = f"🎁 Get FREE Canva Pro for LIFETIME! Hey! I found this amazing bot that gives FREE Canva Pro subscription for lifetime! Join using my link: {referral_link} 💯 100% Working & Verified! 🚀 No payment required! ⚡ Instant activation! Don't miss this opportunity! 🔥"

    # Create inline keyboard with referral buttons only
    referral_markup = types.InlineKeyboardMarkup(row_width=1)
    share_btn = types.InlineKeyboardButton(
        '🚀 Share Your Link',
        switch_inline_query=share_text
    )
    claim_canva_btn = types.InlineKeyboardButton('🎁 Claim My Canva Pro', callback_data='claim_canva')
    referral_markup.add(share_btn, claim_canva_btn)

    # Send a NEW message with the referral info (don't replace the existing one)
    bot.send_message(
        chat_id=call.message.chat.id,
        text=referral_text,
        reply_markup=referral_markup
    )

@bot.callback_query_handler(func=lambda call: call.data == 'claim_canva')
def callback_claim_canva(call):
    # Answer the callback query
    bot.answer_callback_query(call.id)

    # Send first message
    bot.send_message(
        chat_id=call.message.chat.id,
        text="⚡️ Complete your 5 referrals now & get a LIFETIME FREE Canva Pro subscription instantly!"
    )

    # Create inline keyboard with horizontal buttons
    claim_markup = types.InlineKeyboardMarkup(row_width=2)
    buy_btn = types.InlineKeyboardButton('Buy Canva Pro', callback_data='buy_canva')
    complete_btn = types.InlineKeyboardButton('Complete 5 Refer', callback_data='complete_refer')
    claim_markup.row(buy_btn, complete_btn)

    # Send second message with buttons
    bot.send_message(
        chat_id=call.message.chat.id,
        text="❌ Oops! You haven't reached the 5-referral milestone yet.",
        reply_markup=claim_markup
    )

@bot.callback_query_handler(func=lambda call: call.data == 'share_link')
def callback_share_link(call):
    # Get the user's referral link
    user_id = call.from_user.id
    referral_link = f"https://t.me/canva_pro_robot?start={user_id}"

    # Create the share message with inline button that opens contact picker
    share_text = f"🎁 Get FREE Canva Pro for LIFETIME! Hey! I found this amazing bot that gives FREE Canva Pro subscription for lifetime! Join using my link: {referral_link} 💯 100% Working & Verified! 🚀 No payment required! ⚡ Instant activation! Don't miss this opportunity! 🔥"

    # Create inline keyboard with switch_inline_query button to open contact picker
    share_markup = types.InlineKeyboardMarkup()
    share_btn = types.InlineKeyboardButton(
        '📤 Choose Contact to Share',
        switch_inline_query=share_text
    )
    share_markup.add(share_btn)

    # Send message with the share button
    bot.send_message(
        chat_id=call.message.chat.id,
        text="� Click the button below to choose contacts and share your referral link:",
        reply_markup=share_markup
    )

@bot.callback_query_handler(func=lambda call: call.data == 'buy_canva')
def callback_buy_canva(call):
    # Answer the callback query
    bot.answer_callback_query(call.id)

    # Create the pricing table message
    pricing_message = """� Buy Canva Pro Subscription

```
| Plan     | INR Price | Dollar Price | Validity   |
|----------|-----------|--------------|------------|
| 1 Month  | ₹150      | $7         | 1 Month    |
| 1 Year   | ₹170      | $8         | 12 Months  |
| Lifetime | ₹199      | $8.99      | Lifetime   |
```

Please choose option for payment -
1. UPI
2. Crypto
3. Paypal
4. Other Payment Method"""

    # Send message with pricing table
    bot.send_message(
        chat_id=call.message.chat.id,
        text=pricing_message,
        parse_mode='Markdown'
    )

@bot.callback_query_handler(func=lambda call: call.data == 'complete_refer')
def callback_complete_refer(call):
    # Answer the callback query
    bot.answer_callback_query(call.id)

    # Send message about completing referrals
    bot.send_message(
        chat_id=call.message.chat.id,
        text="🚀 Complete Your 5 Referrals!\n\n📊 Current Progress: 0/5\n\n💡 Share your referral link with friends to unlock FREE Canva Pro!\n\n🎁 Each successful referral gets you closer to your goal!"
    )

if __name__ == '__main__':

    bot.polling()
